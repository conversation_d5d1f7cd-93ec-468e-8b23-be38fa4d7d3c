package com.turinggear.ssa_console

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.method.ScrollingMovementMethod
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import android.widget.Toast.LENGTH_LONG
import android.widget.Toast.LENGTH_SHORT
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.FileProvider
import androidx.lifecycle.lifecycleScope
import com.google.android.material.button.MaterialButton
import com.turinggear.ssa_console.service.AbstractGuardService
import com.turinggear.ssa_console.service.GuardService
import com.turinggear.ssa_shared.util.OSUtils
import com.turinggear.ssa_shared.util.SerialNumber
import com.turinggear.ssa_shared.util.ShellCommander
import com.turinggear.ssa_shared.util.UpgradeUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.File
import java.io.IOException
import java.io.InputStreamReader
import java.net.URL
import javax.net.ssl.HttpsURLConnection
import kotlin.system.exitProcess

class MainActivity : AppCompatActivity() {

    private lateinit var tvTitle: TextView
    private lateinit var tvUsage: TextView
    private lateinit var buttonReboot: MaterialButton
    private lateinit var buttonRefresh: MaterialButton
    private lateinit var buttonAppReinstall: MaterialButton
    private lateinit var buttonRootStatus: MaterialButton
    private lateinit var buttonAppStatus: MaterialButton
    private lateinit var buttonInstallConsole: MaterialButton
    private lateinit var buttonSerialNumber: MaterialButton
    private lateinit var buttonOnlineStatus: MaterialButton
    private lateinit var buttonGuardService: MaterialButton
    private lateinit var buttonStopGuardService: MaterialButton

    private var TARGET_APP_PACKAGE_NAME = "com.turinggear.ssa_kiosk"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        supportActionBar?.title = "控制台 ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})"

        TARGET_APP_PACKAGE_NAME = when(Build.MODEL) {
            "AIS906-RK3288" -> {
                "com.turinggear.ssa_car"
            }
            "ZC-339" -> {
                "com.turinggear.ssa_kiosk"
            }
            "rk3288" -> {
                "com.turinggear.ssa_kiosk"
            }
            else -> {
                "com.turinggear.ssa_kiosk"
            }
        }

        startService(Intent(this, GuardService::class.java));
        // Initialize Views
        tvTitle = findViewById(R.id.textViewTitle)
        tvUsage = findViewById(R.id.tvUsage)
        buttonReboot = findViewById(R.id.buttonReboot)
        buttonRefresh = findViewById(R.id.buttonRefresh)
        buttonAppReinstall = findViewById(R.id.buttonAppReinstall)
        buttonRootStatus = findViewById(R.id.buttonRootStatus)
        buttonAppStatus = findViewById(R.id.buttonAppStatus)
        buttonInstallConsole = findViewById(R.id.buttonInstallConsole)
        buttonSerialNumber = findViewById(R.id.buttonSerialNumber)
        buttonOnlineStatus = findViewById(R.id.buttonOnlineStatus)
        buttonGuardService = findViewById(R.id.buttonGuardService)
        buttonStopGuardService = findViewById(R.id.buttonStopGuardService)

        updateUI()

        setupClickListeners()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_main, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_exit -> {
                finishAffinity()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun updateUI() {

        tvTitle.text = "${BuildConfig.FLAVOR}"

        tvUsage.text = getCpuAndMemoryUsage()

        // 1. Root Status
        if (OSUtils.checkRootStatus()) {
            val strategy: String = ShellCommander.determineBestSuStrategy().name
            buttonRootStatus.text = "已Root ($strategy)"
//            buttonRootStatus.setIconResource(R.drawable.ic_check_circle) // You'll need this drawable
        } else {
            buttonRootStatus.text = "不支持Root"
//            buttonRootStatus.setIconResource(R.drawable.ic_cancel) // You'll need this drawable
        }
        buttonRootStatus.isEnabled = false // Always disabled

        // 2. App Installation Status
        if (isAppInstalled(TARGET_APP_PACKAGE_NAME)) {
            buttonAppStatus.text = "已安装: ${TARGET_APP_PACKAGE_NAME.substringAfterLast('.')} (点击安装/启动)"
//            buttonAppStatus.setIconResource(R.drawable.ic_check_circle)
        } else {
            buttonAppStatus.text = "未安装: ${TARGET_APP_PACKAGE_NAME.substringAfterLast('.')} (点击安装/启动)"
//            buttonAppStatus.setIconResource(R.drawable.ic_download) // You'll need this drawable
        }

        if (OSUtils.isSystemApp(this, "com.turinggear.ssa_console")) {
            buttonInstallConsole.text = "安装控制台(系统软件)"
        } else {
            buttonInstallConsole.text = "安装控制台(当前非系统软件)"
        }

        // 3. Serial Number
        buttonSerialNumber.text = "序列号:${SerialNumber.originalSerial()}"
//        buttonSerialNumber.setIconResource(R.drawable.ic_info) // You'll need this drawable

        // 4. Online Status
        if (isDeviceOnline()) {
            buttonOnlineStatus.text = "网络信息 (在线)"
//            buttonOnlineStatus.setIconResource(R.drawable.ic_wifi_on) // You'll need this drawable
        } else {
            buttonOnlineStatus.text = "网络信息 (离线)"
//            buttonOnlineStatus.setIconResource(R.drawable.ic_wifi_off) // You'll need this drawable
        }

        // 5. GuardService Status (Placeholder logic)
        if (isGuardServiceRunning()) {
            buttonGuardService.text = "停止 GuardService"
//            buttonGuardService.setIconResource(R.drawable.ic_stop_service) // You'll need this drawable
        } else {
            buttonGuardService.text = "启动 GuardService"
//            buttonGuardService.setIconResource(R.drawable.ic_start_service) // You'll need this drawable
        }
    }

    private fun setupClickListeners() {
        buttonAppReinstall.setOnClickListener {
            Toast.makeText(this, "开始重新安装,请等待", LENGTH_LONG).show()
            val oss_url = this.resources.getString(com.turinggear.ssa_shared.R.string.oss_url)
            installAppAll(oss_url)
        }

        buttonReboot.setOnClickListener {
            OSUtils.reboot()
        }

        buttonRefresh.setOnClickListener {
            updateUI()
        }

        buttonAppStatus.setOnClickListener {
            showInstallAppDialog(TARGET_APP_PACKAGE_NAME)
        }

        buttonInstallConsole.setOnClickListener {
            showInstallAppDialog("com.turinggear.ssa_console")
        }

        buttonSerialNumber.setOnClickListener {
            showHardwareInfoDialog()
        }

        buttonOnlineStatus.setOnClickListener {
            showNetworkInfoDialog()
        }

        buttonGuardService.setOnClickListener {
            if (isGuardServiceRunning()) {
                stopGuardService()
            } else {
                startGuardService()
            }
            updateUI() // Refresh button text
        }

        buttonStopGuardService.setOnClickListener {
            Log.e("XXX", "is running ${isGuardServiceRunning()}")
            stopGuardService()
        }
    }

    fun getCpuAndMemoryUsage(): String {
        var result = java.lang.StringBuilder()

        try {
            // 获取CPU使用率
            val process = Runtime.getRuntime().exec("top -n 1")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            var line: String?
            var totalCpuUsage = 0f

            while ((reader.readLine().also { line = it }) != null) {
                if (line!!.contains("User")) {
                    val parts =
                        line!!.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                    for (part in parts) {
                        if (part.contains("User")) {
                            totalCpuUsage += part.replace("[^0-9.]".toRegex(), "").toFloat()
                        }
                        if (part.contains("System")) {
                            totalCpuUsage += part.replace("[^0-9.]".toRegex(), "").toFloat()
                        }
                    }
                    break
                }
            }
            reader.close()


            // 获取内存使用率
            val memProcess = Runtime.getRuntime().exec("cat /proc/meminfo")
            val memReader = BufferedReader(InputStreamReader(memProcess.inputStream))
            var totalMem: Long = 0
            var freeMem: Long = 0

            while ((memReader.readLine().also { line = it }) != null) {
                if (line!!.startsWith("MemTotal:")) {
                    totalMem = line!!.split("\\s+".toRegex()).dropLastWhile { it.isEmpty() }
                        .toTypedArray()[1].toLong()
                } else if (line!!.startsWith("MemFree:") || line!!.startsWith("Cached:") || line!!.startsWith(
                        "Buffers:"
                    )
                ) {
                    freeMem += line!!.split("\\s+".toRegex()).dropLastWhile { it.isEmpty() }
                        .toTypedArray()[1].toLong()
                }

                if (totalMem > 0 && freeMem > 0) {
                    break
                }
            }
            memReader.close()

            val memUsagePercent = 100f * (1 - freeMem.toFloat() / totalMem)

            result.append("CPU: ").append(String.format("%.1f", totalCpuUsage)).append("%")
            result.append(" ")
            result.append("内存: ").append(String.format("%.1f", memUsagePercent)).append("%")
        } catch (e: IOException) {
            result = java.lang.StringBuilder("获取系统信息失败")
        }

        return result.toString()
    }


    // --- Helper Functions ---

    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    private fun launchApp(packageName: String) {
        val intent = packageManager.getLaunchIntentForPackage(packageName)
        if (intent != null) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        } else {
            Toast.makeText(this, "无法启动应用: $packageName", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showInstallAppDialog(packageName: String) {
        val oss_url = this.resources.getString(com.turinggear.ssa_shared.R.string.oss_url)
        AlertDialog.Builder(this)
            .setTitle("安装/启动")
            .setMessage("$packageName\n${oss_url}")
            .setPositiveButton("打开软件") { _, _ ->
                if (isAppInstalled(packageName)) {
                    launchApp(packageName)
                } else {
                    Toast.makeText(this, "请先安装软件", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("安装") {_, _ ->
                installApp(packageName, oss_url)
            }
            .show()
    }

    private fun installAppAll(oss_url: String) {
        lifecycleScope.launch {
            var packageNames = arrayOf("com.turinggear.ssa_kiosk", "com.turinggear.ssa_console")
            var ret = true
            for (packageName in packageNames) {
                Toast.makeText(applicationContext, "开始下载 ${packageName}", LENGTH_LONG).show()
                val url = UpgradeUtil.defaultApkUrl(oss_url, packageName)
                try {
                    ret = UpgradeUtil.download(this@MainActivity, packageName, url)
                } catch (e: Exception) {
                    Log.e("XXX", "error ${e.localizedMessage}")
                    Toast.makeText(this@MainActivity, e.localizedMessage, LENGTH_SHORT).show()

                    ret = false
                }
                if (ret) {
                    val path = UpgradeUtil.downloadedFilePath(this@MainActivity, packageName)
                    Toast.makeText(applicationContext, "已下载 ${path}", LENGTH_LONG).show()
                } else {
                    ret = false
                    break
                }

                val isRooted = OSUtils.checkRootStatus()
                try {
                    if (isRooted) {
                        ret = UpgradeUtil.installAsSystemApp(this@MainActivity, packageName)
                    } else {
                        ret = UpgradeUtil.installInteractively(this@MainActivity, packageName)
                    }
                } catch (e: Exception) {
                    Log.e("XXX", "error ${e.localizedMessage}")
                    Toast.makeText(this@MainActivity, e.localizedMessage, LENGTH_SHORT).show()

                    ret = false
                }

                if (!ret) {
                    break
                }
            }

            if (ret) {
                AlertDialog.Builder(this@MainActivity)
                    .setTitle("安装成功")
                    .setMessage("需要重启生效")
                    .setPositiveButton("!立即重启!") { _, _ ->
                        OSUtils.reboot()
                    }
                    .setNegativeButton("暂不重启", null)
                    .show()
            } else {
                AlertDialog.Builder(this@MainActivity)
                    .setTitle("安装失败")
                    .setMessage("安装失败")
                    .setPositiveButton("确定", null)
                    .show()
            }
        }
    }

    private fun installApp(packageName: String, oss_url: String) {
        lifecycleScope.launch {
            Toast.makeText(applicationContext, "开始下载", LENGTH_LONG).show()
            buttonAppStatus.isEnabled = false
            val url = UpgradeUtil.defaultApkUrl(oss_url, packageName)
            var ret = false
            try {
                ret = UpgradeUtil.download(this@MainActivity, packageName, url)
            } catch (e: Exception) {
                Log.e("XXX", "error ${e.localizedMessage}")
                Toast.makeText(this@MainActivity, e.localizedMessage, LENGTH_SHORT).show()

                ret = false
            }
            if (ret) {
                val path = UpgradeUtil.downloadedFilePath(this@MainActivity, packageName)
                Toast.makeText(applicationContext, "已下载 ${path}", LENGTH_LONG).show()
                buttonAppStatus.isEnabled = true
            } else {
                AlertDialog.Builder(this@MainActivity)
                    .setTitle("下载失败 ${packageName}")
                    .setMessage("URL: $url")
                    .setPositiveButton("确定", null)
                    .show()

//                Toast.makeText(applicationContext, "下载失败", LENGTH_LONG).show()
                buttonAppStatus.isEnabled = true

                return@launch
            }

            val isRooted = OSUtils.checkRootStatus()
            try {
                if (isRooted) {
                    ret = UpgradeUtil.installAsSystemApp(this@MainActivity, packageName)
                }
                else {
                    ret = UpgradeUtil.installInteractively(this@MainActivity, packageName)
                }
            } catch (e: Exception) {
                Log.e("XXX", "error ${e.localizedMessage}")
                Toast.makeText(this@MainActivity, e.localizedMessage, LENGTH_SHORT).show()

                ret = false
            }
            if (ret) {
                AlertDialog.Builder(this@MainActivity)
                    .setTitle("安装成功")
                    .setMessage("URL: $url\n\n需要重启生效")
                    .setPositiveButton("!立即重启!") { _, _ ->
                        OSUtils.reboot()
                    }
                    .setNegativeButton("暂不重启", null)
                    .show()
            } else {
                AlertDialog.Builder(this@MainActivity)
                    .setTitle("安装失败")
                    .setMessage("URL: $url")
                    .setPositiveButton("确定", null)
                    .show()
            }
        }
        // Example: Intent to search on Play Store (if app is there)
        // try {
        //     startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName")))
        // } catch (e: android.content.ActivityNotFoundException) {
        //     startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=$packageName")))
        // }
    }

    private fun getDetailedHardwareInfo(): String {
        val sb = StringBuilder()
//        sb.append("固定序列号 (显示): $FIXED_SERIAL_NUMBER\n\n") // Displayed fixed one

//        // Actual Hardware Serial (requires READ_PHONE_STATE before Android 10, restricted on/after Android 10)
//        try {
//            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
//                if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
//                    val actualSerial = Build.getSerial()
//                    sb.append("真实硬件序列号: $actualSerial\n")
//                } else {
//                    sb.append("真实硬件序列号: (需要READ_PHONE_STATE权限)\n")
//                }
//            } else {
//                sb.append("真实硬件序列号: (在Android 10+上受限)\n")
//            }
//        } catch (e: SecurityException) {
//            sb.append("真实硬件序列号: (获取时安全异常)\n")
//        }


        sb.append("Android ID: ${Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)}\n")
        sb.append("设备型号: ${Build.MODEL}\n")
        sb.append("制造商: ${Build.MANUFACTURER}\n")
        sb.append("品牌: ${Build.BRAND}\n")
        sb.append("硬件: ${Build.HARDWARE}\n")
        sb.append("主板: ${Build.BOARD}\n")
        sb.append("显示: ${Build.DISPLAY}\n")
        sb.append("SDK版本: ${Build.VERSION.SDK_INT}\n")
        sb.append("版本号: ${Build.VERSION.RELEASE}\n\n")

        // Memory Info (basic)
        sb.append("内存信息:\n")
        try {
            val process = Runtime.getRuntime().exec("cat /proc/meminfo")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            var line: String?
            var linesRead = 0
            while (reader.readLine().also { line = it } != null && linesRead < 10) { // Limit lines
                sb.append(line).append("\n")
                linesRead++
            }
            reader.close()
            process.destroy()
        } catch (e: Exception) {
            sb.append("  无法读取内存信息: ${e.message}\n")
        }
        sb.append("\n")

        sb.append("设备温度: \n")
        var temp_path = "/sys/class/thermal/thermal_zone0/temp"
        sb.append("$temp_path: \n")
        try {
            val process = Runtime.getRuntime().exec("cat $temp_path")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            var line: String?
            var linesRead = 0
            while (reader.readLine().also { line = it } != null && linesRead < 10) { // Limit lines
                sb.append(line).append("\n")
                linesRead++
            }
            reader.close()
            process.destroy()
        } catch (e: Exception) {
            sb.append("  无法读取温度信息: ${e.message}\n")
        }
        sb.append("\n")

        sb.append("blk:\n")
        try {
            val process = Runtime.getRuntime().exec("ls /dev/block/platform/")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            var line: String?
            var linesRead = 0
            while (reader.readLine().also { line = it } != null && linesRead < 10) { // Limit lines
                sb.append(line).append("\n")
                linesRead++
            }
            reader.close()
            process.destroy()
        } catch (e: Exception) {
            sb.append("  无法读取blk信息: ${e.message}\n")
        }
        sb.append("\n")

        return sb.toString()
    }

    private fun showHardwareInfoDialog() {
        val scrollView = ScrollView(this)
        val textView = TextView(this).apply {
            text = getDetailedHardwareInfo()
            setPadding(40, 40, 40, 40)
            movementMethod = ScrollingMovementMethod.getInstance() // Enable scrolling within TextView if needed
            setTextIsSelectable(true)
        }
        scrollView.addView(textView)

        AlertDialog.Builder(this)
            .setTitle("设备硬件信息")
            .setView(scrollView)
            .setPositiveButton("关闭", null)
            .show()
    }

    private fun isDeviceOnline(): Boolean {
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val activeNetwork = connectivityManager.getNetworkCapabilities(network) ?: return false
        return when {
            activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
            activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
            activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> true
            else -> false
        }
    }

    private suspend fun getNetworkInfo(): String {
        val sb = StringBuilder()
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        sb.append("当前网络状态: ${if (isDeviceOnline()) "在线" else "离线"}\n\n")

        if (isDeviceOnline()) {
            val activeNetwork = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(activeNetwork)

            if (capabilities != null) {
                if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                    sb.append("类型: Wi-Fi\n")
                    // Getting Wi-Fi specific info like SSID might require ACCESS_WIFI_STATE and location permissions
                }
                if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    sb.append("类型: 移动数据\n")
                    // Getting cellular specific info might require READ_PHONE_STATE
                }
                if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) {
                    sb.append("类型: 以太网\n")
                }

                // IP Address (basic, might show local IP)
                val linkProperties = connectivityManager.getLinkProperties(activeNetwork)
                linkProperties?.linkAddresses?.forEach { linkAddress ->
                    sb.append("IP地址: ${linkAddress.address.hostAddress}\n")
                }

                // 访问 aliyun.com and baidu.com
                sb.append("网站联通性测试。\n")
                sb.append(checkWebsiteStatus("https://status.aliyun.com") + "\n")
                sb.append(checkWebsiteStatus("https://www.baidu.com") + "\n")

            } else {
                sb.append("无法获取网络详情。\n")
            }
        } else {
            sb.append("无活动的网络连接。\n")
        }
        return sb.toString()
    }

    private suspend fun checkWebsiteStatus(url: String): String {
        return try {
            val connection = URL(url).openConnection() as HttpsURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 5000 // 5 seconds timeout
            connection.readTimeout = 5000 // 5 seconds timeout
            val responseCode = connection.responseCode
            "$url [$responseCode]"
        } catch (e: Exception) {
            "$url 异常 ${e.toString()}"
        }
    }

    private fun showNetworkInfoDialog() {
        lifecycleScope.launch {
            try {
                // 使用 withContext 将网络操作移至 IO 线程
                val msg = withContext(Dispatchers.IO) {
                    getNetworkInfo()
                }

                // 在获取到网络信息后显示对话框
                AlertDialog.Builder(this@MainActivity)
                    .setTitle("网络信息")
                    .setMessage(msg)
                    .setPositiveButton("关闭", null)
                    .show()
            } catch (e: Exception) {
                // 处理可能的异常
                Toast.makeText(this@MainActivity, "获取网络信息失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // --- GuardService Placeholder Functions ---
    private fun isGuardServiceRunning(): Boolean {
        Log.i("XXX", "is running ${AbstractGuardService.isRunning()}")
        // TODO, use AIDL
        return true
    }

    private fun startGuardService() {
        Toast.makeText(this, "启动保护服务", Toast.LENGTH_SHORT).show()
        val serviceIntent = Intent(this, GuardService::class.java)
        try {
            startService(serviceIntent) // 启动 Service
            Log.i("MainActivity", "startService() called for GuardService.")
        } catch (e: Exception) {
        }
    }

    private fun stopGuardService() {
        Toast.makeText(this, "停止保护服务...", Toast.LENGTH_SHORT).show()
        val serviceIntent = Intent(this, GuardService::class.java)
        serviceIntent.action = AbstractGuardService.ACTION_STOP_SERVICE
        startService(serviceIntent)
//        val stopped = stopService(serviceIntent) // 停止 Service
//        if (stopped) {
//            Log.e("XXX", "stopped guard")
//        } else {
//            Log.e("XXX", "can not stop guard")
//        }
    }
}