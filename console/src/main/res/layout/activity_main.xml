<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="16dp"
    tools:context=".MainActivity">

    <TextView
        android:id="@+id/textViewTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="道合智行控制台"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/buttonRefresh"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="刷新信息"
        android:textColor="?android:attr/textColorPrimary" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardViewLegend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/textViewTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="24dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:contentPadding="16dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:padding="16dp"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonAppReinstall"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="重新安装全部App" />

            <com.google.android.material.button.MaterialButton
            android:id="@+id/buttonAppStatus"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="检测App状态..." />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonInstallConsole"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                android:text="安装控制台应用" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonSerialNumber"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="序列号: (固定值)" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonOnlineStatus"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="检测网络状态..." />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonGuardService"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                android:text="检测GuardService..." />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonStopGuardService"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="停止保护服务" />

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <LinearLayout
        android:layout_width="wrap_content"
        android:padding="16dp"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/buttonReboot"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="重启系统"
            android:textColor="?android:attr/textColorPrimary" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/buttonRootStatus"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="检测Root状态..."
            android:enabled="false"
            android:textColor="?android:attr/textColorPrimary" />


        <TextView
            android:id="@+id/tvUsage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:textAlignment="center"
            android:textColor="?android:attr/textColorPrimary" />
    </LinearLayout>

</LinearLayout>