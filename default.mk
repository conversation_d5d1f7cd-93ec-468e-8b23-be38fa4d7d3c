clean:
	../gradlew clean
bump:
	../gradlew bumpPatch
minor:
	../gradlew bumpMinor

upload_%: copy
	$(eval APP := $(notdir $(CURDIR)))
	$(eval SA := $(lastword $(subst _, ,$@)))
	echo "uploading to download page as $(APP).apk"
	$(eval CFG := $(shell if [ -f ~/.ossutilconfig_$(SA) ]; then echo ~/.ossutilconfig_$(SA); else echo ~/.ossutilconfig_daohezhixing; fi))
	#ossutil -c ~/.ossutilconfig_$(SA) cp -f ../apks/$(APP)-*-$(SA)-release.apk oss://ssa-static-$(SA)/releases/$(APP).apk
	if [ "$(APP)" = "console" ]; then \
		ossutil -c $(CFG) cp -f ../apks/$(APP)-*-s906-release.apk oss://ssa-static-$(SA)/releases/$(APP)-s906.apk; \
		ossutil -c $(CFG) cp -f ../apks/$(APP)-*-zc339a-release.apk oss://ssa-static-$(SA)/releases/$(APP)-zc339a.apk; \
		ossutil -c $(CFG) cp -f ../apks/$(APP)-*-rk3288-release.apk oss://ssa-static-$(SA)/releases/$(APP)-rk3288.apk; \
	else \
		ossutil -c $(CFG) cp -f ../apks/$(APP)-*-$(SA)-release.apk oss://ssa-static-$(SA)/releases/$(APP).apk; \
	fi

# outputs
ls:
	@find . -name "*-release.apk" | \grep "build/outputs/apk"
copy:
	mkdir -p ../apks
	find . -name "*-release.apk" | \grep "build/outputs/apk" | xargs -I {} cp {} ../apks

# for dev
dev_screenshot:
	adb shell screencap -p /sdcard/screencap.png && adb pull /sdcard/screencap.png && adb shell rm /sdcard/screencap.png
dev_screenrecord:
	adb shell screenrecord /sdcard/screenrecord.mp4 && adb pull /sdcard/screenrecord.mp4 && adb shell rm /sdcard/screenrecord.mp4
dev_stop:
	$(eval APP := $(notdir $(CURDIR)))
	adb shell am force-stop "com.turinggear.ssa_$(APP)"
uninstall_sys:
	$(eval APP := $(notdir $(CURDIR)))
	-adb uninstall com.turinggear.ssa_$(APP)
	adb root
	adb remount
	-adb shell pm uninstall --user 0 com.turinggear.ssa_$(APP)
	adb shell rm /system/priv-app/*.apk
	@echo "need reboot!"

## variants
build:
	../gradlew assembleRelease

yqh:
	../gradlew assembleYqh

shiyuan:
	../gradlew assembleShiyuan

waterpark:
	../gradlew assembleWaterpark

jiuzhouwa:
	../gradlew assembleJiuzhouwa

haituo:
	../gradlew assembleHaituo

nbh:
	../gradlew assembleNbh

cssh:
	../gradlew assembleCssh

hdboat:
	../gradlew assembleHdboat

main:
	../gradlew assembleMain

csshboat:
	../gradlew assembleCsshboat

shg:
	../gradlew assembleShg

lihuboat:
	../gradlew assembleLihuboat
    
fhxdboat:
	../gradlew assembleFhxdboat
    
