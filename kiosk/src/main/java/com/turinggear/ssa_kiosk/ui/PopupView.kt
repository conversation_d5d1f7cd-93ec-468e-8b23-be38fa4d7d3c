package com.turinggear.ssa_kiosk.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Add
import androidx.compose.material.icons.outlined.Close
import androidx.compose.material.icons.outlined.Remove
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.turinggear.ssa_kiosk.BuildConfig
import com.turinggear.ssa_kiosk.util.MediaManager
import com.turinggear.ssa_shared.*
import com.turinggear.ssa_shared.R

@Composable
fun PopupView() {
    val context = LocalContext.current
    val enableRouteDirection = context.resources.getBoolean(R.bool.enable_route_direction)
    Box(
        modifier = Modifier
            .clickable(enabled = false, onClick = { })
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.8f)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            modifier = Modifier
                .clickable(enabled = false, onClick = { })
                .width(320.dp)
                .border(
                    1.dp,
                    MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                    RoundedCornerShape(10.dp)
                )
                .clip(RoundedCornerShape(10.dp))
                .background(MaterialTheme.colors.background)
                .aspectRatio(if ((BuildConfig.FLAVOR == "yqh" || BuildConfig.FLAVOR == "main")) 0.65f else 0.80f),
            horizontalAlignment = Alignment.Start
        ) {
            Box(
                contentAlignment = Alignment.TopEnd
            ) {
                Column(
                    Modifier
                        .clickable {
                            SHOW_POPUP = false
                            SCAN_TYPE = SCANTYPE.CALL_CAR
                            PAYMENT_STATE = PAYMENTSTATE.UNSTARTED
                            SELECTED_AMOUNT = 1
                            if (enableRouteDirection) {
                                SELECTED_DIRECTION_STATION_ID = null
                            }
                            MediaManager.stopMusic()
                        }
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp)
                        .padding(top = 30.dp, bottom = 20.dp),
                    verticalArrangement = Arrangement.spacedBy(10.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    val categoryName = GOOD_CATEGORY_MAP[SELECTED_GOOD?.category] ?: ""
                    Text(
                        text = categoryName,
                        color = MaterialTheme.colors.onBackground.copy(0.5f),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.caption
                    )
                    Text(
                        text = SELECTED_GOOD?.name ?: "",
                        color = MaterialTheme.colors.onBackground.copy(0.9f),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.body1
                    )
                }
                TextButton(
                    onClick = {
                        SHOW_POPUP = false
                        SCAN_TYPE = SCANTYPE.CALL_CAR
                        PAYMENT_STATE = PAYMENTSTATE.UNSTARTED
                        SELECTED_AMOUNT = 1
                        if (enableRouteDirection) {
                            SELECTED_DIRECTION_STATION_ID = null
                        }
                        MediaManager.stopMusic()
                    },
                    modifier = Modifier
                        .padding(all = 5.dp)
                        .size(36.dp)
                        .aspectRatio(1f),
                    colors = ButtonDefaults.textButtonColors(backgroundColor = Color.Transparent),
                    contentPadding = PaddingValues(all = 0.dp)
                ) {
                    Icon(
                        Icons.Outlined.Close,
                        contentDescription = null,
                        modifier = Modifier.size(30.dp),
                        tint = MaterialTheme.colors.onBackground.copy(0.5f)
                    )
                }
            }
            Divider(thickness = 0.5.dp)
            Column(
                Modifier
                    .weight(6f)
                    .padding(vertical = 10.dp),
                verticalArrangement = Arrangement.spacedBy(
                    15.dp,
                    alignment = Alignment.CenterVertically
                )
            ) {
                val startPadding = 55.dp
                Row(
                    Modifier.padding(start = startPadding),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "单价：",
                        color = MaterialTheme.colors.onBackground.copy(0.9f),
                        style = MaterialTheme.typography.h6
                    )
                    Text(
                        text = "${SELECTED_GOOD?.price}元",
                        modifier = Modifier.padding(start = 20.dp),
                        color = MaterialTheme.colors.onBackground.copy(0.9f),
                        style = MaterialTheme.typography.h6
                    )
                }
                Row(
                    Modifier.padding(horizontal = startPadding),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "张数：",
                        color = MaterialTheme.colors.onBackground.copy(0.9f),
                        style = MaterialTheme.typography.h6
                    )
                    Row(
                        modifier = Modifier
                            .padding(start = 20.dp)
                            .border(
                                0.5.dp,
                                MaterialTheme.colors.onSurface.copy(0.3f),
                                RoundedCornerShape(4.dp)
                            )
                            .clip(RoundedCornerShape(4.dp)),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Button(
                            onClick = {
                                if (SELECTED_AMOUNT > 1) {
                                    SELECTED_AMOUNT -= 1
                                }
                            },
                            modifier = Modifier
                                .height(34.dp)
                                .width(40.dp),
                            enabled = (PAYMENT_STATE == PAYMENTSTATE.UNSTARTED),
                            shape = RoundedCornerShape(0),
                            colors = ButtonDefaults.outlinedButtonColors(),
                            contentPadding = PaddingValues(all = 4.dp)
                        ) {
                            Icon(
                                Icons.Outlined.Remove,
                                contentDescription = null,
                                modifier = Modifier.size(24.dp),
                                tint = MaterialTheme.colors.onBackground.copy(0.9f)
                            )
                        }
                        Text(
                            text = SELECTED_AMOUNT.toString(),
                            modifier = Modifier.padding(horizontal = 14.dp),
                            style = MaterialTheme.typography.h6
                        )
                        Button(
                            onClick = { SELECTED_AMOUNT += 1 },
                            modifier = Modifier
                                .height(34.dp)
                                .width(40.dp),
                            enabled = (PAYMENT_STATE == PAYMENTSTATE.UNSTARTED),
                            shape = RoundedCornerShape(0),
                            colors = ButtonDefaults.outlinedButtonColors(),
                            contentPadding = PaddingValues(all = 4.dp)
                        ) {
                            Icon(
                                Icons.Outlined.Add,
                                contentDescription = null,
                                modifier = Modifier.size(24.dp),
                                tint = MaterialTheme.colors.onBackground.copy(0.9f)
                            )
                        }
                    }
                }
                Row(
                    Modifier.padding(horizontal = startPadding),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "总价：",
                        color = MaterialTheme.colors.onBackground.copy(0.9f),
                        style = MaterialTheme.typography.h6
                    )
                    val total =
                        SELECTED_GOOD?.price?.toFloat()?.times(SELECTED_AMOUNT.toFloat())
                    Text(
                        text = "%.1f元".format(total),
                        modifier = Modifier.padding(start = 20.dp),
                        color = MaterialTheme.colors.onSurface,
                        style = MaterialTheme.typography.h6
                    )
                }
                val enableRouteDirection = context.resources.getBoolean(R.bool.enable_route_direction)
                if (enableRouteDirection) {
                    FIRST_STATION_FOR_SELECTED_GOOD?.let { firstStation ->
                        LAST_STATION_FOR_SELECTED_GOOD?.let { lastStation ->
                            if (firstStation.id == CURRENT_STATION_ID) {
                                SELECTED_DIRECTION_STATION_ID = lastStation.id.toString()
                            }
                            if (lastStation.id == CURRENT_STATION_ID) {
                                SELECTED_DIRECTION_STATION_ID = firstStation.id.toString()
                            }

                            // 获取当前站点的方向禁用规则
                            val currentStationRule = SELECTED_GOOD?.metadata?.station_rules?.find {
                                it.station_id == CURRENT_STATION_ID
                            }
                            val isFirstDirectionDisabled = currentStationRule?.disabled_direction == 1
                            val isLastDirectionDisabled = currentStationRule?.disabled_direction == 2

                            Row(
                                Modifier.padding(start = startPadding, end = 10.dp),
                                verticalAlignment = Alignment.Top
                            ) {
                                Text(
                                    text = "方向：",
                                    color = MaterialTheme.colors.onBackground.copy(0.9f),
                                    style = MaterialTheme.typography.h6
                                )
                                Column(modifier = Modifier.padding(start = 5.dp)) {
                                    Row(
                                        Modifier
                                            .fillMaxWidth()
                                            .height(30.dp)
                                            .selectable(
                                                selected = true,
                                                enabled = (firstStation.id != CURRENT_STATION_ID) && !isFirstDirectionDisabled,
                                                onClick = {
                                                    SELECTED_DIRECTION_STATION_ID =
                                                        firstStation.id.toString()
                                                }
                                            ),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        RadioButton(
                                            selected = (SELECTED_DIRECTION_STATION_ID == firstStation.id.toString()),
                                            enabled = (firstStation.id != CURRENT_STATION_ID) && !isFirstDirectionDisabled,
                                            onClick = {
                                                SELECTED_DIRECTION_STATION_ID =
                                                    firstStation.id.toString()
                                            },
                                            colors = RadioButtonDefaults.colors(
                                                selectedColor = Color.Blue,
                                                disabledColor = MaterialTheme.colors.onSurface.copy(
                                                    alpha = 0.2f
                                                )
                                            )
                                        )
                                        val color = if (firstStation.id == CURRENT_STATION_ID || isFirstDirectionDisabled) {
                                            MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
                                        } else {
                                            Color.Unspecified
                                        }
                                        Text(
                                            text = firstStation.name + if (firstStation.id == CURRENT_STATION_ID) " (本站)" else "",
                                            color = color,
                                            style = MaterialTheme.typography.caption
                                        )
                                    }
                                    Row(
                                        Modifier
                                            .fillMaxWidth()
                                            .height(30.dp)
                                            .selectable(
                                                selected = true,
                                                enabled = (lastStation.id != CURRENT_STATION_ID) && !isLastDirectionDisabled,
                                                onClick = {
                                                    SELECTED_DIRECTION_STATION_ID =
                                                        lastStation.id.toString()
                                                }
                                            ),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        RadioButton(
                                            selected = (SELECTED_DIRECTION_STATION_ID == lastStation.id.toString()),
                                            enabled = (lastStation.id != CURRENT_STATION_ID) && !isLastDirectionDisabled,
                                            onClick = {
                                                SELECTED_DIRECTION_STATION_ID =
                                                    lastStation.id.toString()
                                            },
                                            colors = RadioButtonDefaults.colors(
                                                selectedColor = Color.Blue,
                                                disabledColor = MaterialTheme.colors.onSurface.copy(
                                                    alpha = 0.2f
                                                )
                                            )
                                        )
                                        val color = if (lastStation.id == CURRENT_STATION_ID || isLastDirectionDisabled) {
                                            MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
                                        } else {
                                            Color.Unspecified
                                        }
                                        Text(
                                            text = lastStation.name + if (lastStation.id == CURRENT_STATION_ID) " (本站)" else "",
                                            color = color,
                                            style = MaterialTheme.typography.caption
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Divider(thickness = 0.5.dp)
            WechatPayButton(enableRouteDirection, modifier = Modifier.height(120.dp))
        }
    }
}